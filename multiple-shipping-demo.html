<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Shipping Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .email-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* Multiple Shipping Styles */
        .multiple-shipping-group {
            margin-bottom: 30px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .multiple-shipping-group h5 {
            color: #333;
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        
        .shipping-address {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #007cba;
        }
        
        .shipping-address h6 {
            color: #007cba;
            font-size: 14px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .shipping-address p {
            margin: 5px 0;
            line-height: 1.4;
        }
        
        .multiple-shipping-items {
            background-color: #fff;
            border-radius: 5px;
            width: 100%;
        }
        
        .multiple-shipping-separator {
            border: none;
            height: 2px;
            background: linear-gradient(to right, transparent, #007cba, transparent);
            margin: 20px 0;
        }
        
        .order-list__item {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .order-list__item:last-child {
            border-bottom: none;
        }
        
        .order-list__item__cell {
            padding: 15px;
        }
        
        .order-list__item table {
            width: 100%;
        }
        
        .order-list__image-cell {
            width: 80px;
            vertical-align: top;
        }
        
        .order-list__product-image {
            border-radius: 5px;
        }
        
        .order-list__product-description-cell {
            padding-left: 15px;
            vertical-align: top;
        }
        
        .order-list__item-title {
            font-weight: bold;
            color: #333;
        }
        
        .order-list__item-variant {
            color: #666;
            font-size: 14px;
        }
        
        .order-list__price-cell {
            text-align: right;
            vertical-align: top;
            width: 100px;
        }
        
        .order-list__item-price {
            font-weight: bold;
            color: #007cba;
        }
        
        .multiple-shipping-summary {
            background-color: #fff;
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .multiple-shipping-summary h5 {
            color: #007cba;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .multiple-shipping-summary-table {
            width: 100%;
            margin-bottom: 15px;
        }
        
        .multiple-shipping-summary-table td {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .multiple-shipping-summary-table td:first-child {
            width: 40%;
        }
        
        .multiple-shipping-note {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin: 0;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <h2>ご購入頂きありがとうございました！</h2>
        <p>注文の発送準備を行なっております。商品を発送いたしましたら、改めてお知らせいたします。</p>
        
        <h3>注文サマリー</h3>
        
        <!-- Multiple Shipping Order Demo -->
        <h4>複数配送先注文</h4>
        
        <!-- Shipping Group 1 -->
        <div class="multiple-shipping-group">
            <h5>配送グループ 1</h5>
            
            <!-- Full Address -->
            <div class="shipping-address">
                <h6>配送先住所</h6>
                <p>
                    Sir Tran Tran Nguyen Ngoc<br>
                    Weblife<br>
                    27 Ly Tu Trong<br>
                    1-1 千代田ビル 202号222222<br>
                    Da Nang, Kanagawa 1231231<br>
                    Japan<br>
                    電話: 09891220111
                </p>
                <p><strong>配達希望日:</strong> 指定なし</p>
                <p><strong>配達希望時間:</strong> 指定なし</p>
            </div>
            
            <!-- Items for this group -->
            <table class="multiple-shipping-items">
                <tr class="order-list__item">
                    <td class="order-list__item__cell">
                        <table>
                            <tr>
                                <td class="order-list__image-cell">
                                    <img src="https://via.placeholder.com/60x60/007cba/white?text=Product" 
                                         width="60" height="60" class="order-list__product-image"/>
                                </td>
                                <td class="order-list__product-description-cell">
                                    <span class="order-list__item-title">The Videographer Snowboard × 3</span><br/>
                                    <span class="order-list__item-variant">Default Title</span>
                                </td>
                                <td class="order-list__price-cell">
                                    <p class="order-list__item-price">¥45,000</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            
            <hr class="multiple-shipping-separator">
        </div>
        
        <!-- Shipping Group 2 -->
        <div class="multiple-shipping-group">
            <h5>配送グループ 2</h5>
            
            <!-- Address ID -->
            <div class="shipping-address">
                <h6>配送先住所 (ID: 1234567889)</h6>
                <p>※ お客様の登録住所から選択</p>
            </div>
            
            <!-- Items for this group -->
            <table class="multiple-shipping-items">
                <tr class="order-list__item">
                    <td class="order-list__item__cell">
                        <table>
                            <tr>
                                <td class="order-list__image-cell">
                                    <img src="https://via.placeholder.com/60x60/007cba/white?text=Product" 
                                         width="60" height="60" class="order-list__product-image"/>
                                </td>
                                <td class="order-list__product-description-cell">
                                    <span class="order-list__item-title">Another Product × 2</span><br/>
                                    <span class="order-list__item-variant">Blue / Large</span>
                                </td>
                                <td class="order-list__price-cell">
                                    <p class="order-list__item-price">¥30,000</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        
        <!-- Multiple Shipping Summary -->
        <div class="multiple-shipping-summary">
            <h5>複数配送先注文サマリー</h5>
            <table class="multiple-shipping-summary-table">
                <tr>
                    <td><strong>配送先数:</strong></td>
                    <td>2箇所</td>
                </tr>
                <tr>
                    <td><strong>総商品数:</strong></td>
                    <td>5個</td>
                </tr>
            </table>
            <p class="multiple-shipping-note">
                <em>※ 各配送先に個別に商品をお届けいたします。配送料は配送先ごとに計算されます。</em>
            </p>
        </div>
    </div>
</body>
</html>
