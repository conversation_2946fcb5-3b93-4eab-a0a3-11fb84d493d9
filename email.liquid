{% assign delivery_method_types = delivery_agreements | map: 'delivery_method_type' | uniq %}
{% if delivery_method_types.size > 1 %}
  {% assign has_split_cart = true %}
{% else %}
  {% assign has_split_cart = false %}
{% endif %}

{% capture email_title %}
  {% if has_pending_payment %}
    ご注文頂きありがとうございました。
  {% else %}
    ご購入頂きありがとうございました！
  {% endif %}
{% endcapture %}
{% capture email_body %}
  {% if has_pending_payment %}
    {% if buyer_action_required %}
      支払い完了後、確認メールが届きます。
    {% else %}
      決済が処理されています。注文が確認されるとメールが届きます。
    {% endif %}
  {% else %}
    {% if requires_shipping %}
    {% case delivery_method %}
        {% when 'pick-up' %}
          注文の受取の準備が整うと、メールが届きます。
        {% when 'local' %}
          {{ customer.last_name }}様様、ご注文の品を配達する準備を行っております。
        {% else %}
          注文の発送準備を行なっております。商品を発送いたしましたら、改めてお知らせいたします。
      {% endcase %}
        {% if delivery_instructions != blank  %}
          <p><b>配達情報:</b> {{ delivery_instructions }}</p>
        {% endif %}
       {% if consolidated_estimated_delivery_time %}
        {% if has_multiple_delivery_methods %}
          <h3 class="estimated_delivery__title">配達予定</h3>
          <p>{{ consolidated_estimated_delivery_time }}</p>
        {% else %}
          <p>
            配達予定 <b>{{ consolidated_estimated_delivery_time }}</b>
          </p>
        {% endif %}
       {% endif %}
    {% endif %}
  {% endif %}
  {% assign gift_card_line_items = line_items | where: "gift_card" %}
  {% assign found_gift_card_with_recipient_email = false %}
  {% for line_item in gift_card_line_items %}
    {% if line_item.properties["__shopify_send_gift_card_to_recipient"] and line_item.properties["Recipient email"] %}
      {% assign found_gift_card_with_recipient_email = true %}
      {% break %}
    {% endif %}
  {% endfor %}
  {% if found_gift_card_with_recipient_email %}
    <p>ギフトカードの受取人には、ギフトカードコードが記載されたメールが届きます。</p>
  {% elsif gift_card_line_items.first %}
    <p>ギフトカードの個別のメールが届きます。</p>
  {% endif %}
{% endcapture %}

<!DOCTYPE html>
<html lang="ja">
  <head>
  <title>{{ email_title }}</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" type="text/css" href="/assets/notifications/styles.css">
  <style>
    .button__cell { background: {{ shop.email_accent_color }}; }
    a, a:hover, a:active, a:visited { color: {{ shop.email_accent_color }}; }
  </style>
</head>

  <body>
    <table class="body">
      <tr>
        <td>
          <table class="header row">
  <tr>
    <td class="header__cell">
      <center>

        <table class="container">
          <tr>
            <td>

              <table class="row">
                <tr>
                  <td class="shop-name__cell">
                    {%- if shop.email_logo_url %}
                      <img src="{{shop.email_logo_url}}" alt="{{ shop.name }}" width="{{ shop.email_logo_width }}">
                    {%- else %}
                      <h1 class="shop-name__text">
                        <a href="{{shop.url}}">{{ shop.name }}</a>
                      </h1>
                    {%- endif %}
                  </td>

                    <td>
                      <table class="order-po-number__container">
                        <tr>
                          <td class="order-number__cell">
                            <span class="order-number__text">
                              注文 {{ order_name }}
                            </span>
                          </td>
                        </tr>
                        {%- if po_number %}
                            <tr>
                              <td class="po-number__cell">
                                <span class="po-number__text">
                                  注文書番号 #{{ po_number }}
                                </span>
                              </td>
                            </tr>
                        {%- endif %}
                      </table>
                    </td>
                </tr>
              </table>

            </td>
          </tr>
        </table>

      </center>
    </td>
  </tr>
</table>

          <table class="row content">
  <tr>
    <td class="content__cell">
      <center>
        <table class="container">
          <tr>
            <td>
              
            <h2>{{ email_title }}</h2>
            <p>{{ email_body }}</p>
            {% assign transaction_count = transactions | size %}
  {% if transaction_count > 0 %}
    {% for transaction in transactions %}
      {% if transaction.show_buyer_pending_payment_instructions? %}
        <p> {{transaction.buyer_pending_payment_notice}} </p>
        <p>
        <table class="row">
          <tr>
            {% for instruction in transaction.buyer_pending_payment_instructions %}
              <td>{{ instruction.header }}</td>
            {% endfor %}
            <td>金額</td>
          </tr>
          <tr>
            {% for instruction in transaction.buyer_pending_payment_instructions %}
              <td>{{ instruction.value }}</td>
            {% endfor %}
            <td>{{transaction.amount | money}}</td>
          </tr>
        </table>
        </p>
      {% endif %}
    {% endfor%}
  {% endif %}

            {% if order_status_url %}
              <table class="row actions">
  <tr>
    <td class="empty-line">&nbsp;</td>
  </tr>
  <tr>
    <td class="actions__cell">
      <table class="button main-action-cell">
        <tr>
          <td class="button__cell"><a href="{{ order_status_url }}" class="button__text">注文を表示する</a></td>
        </tr>
      </table>
      {% if shop.url %}
    <table class="link secondary-action-cell">
      <tr>
        <td class="link__cell">または<a href="{{ shop.url }}">ショップにアクセスする</a></td>
      </tr>
    </table>
{% endif %}

    </td>
  </tr>
</table>

            {% else %}
              {% if shop.url %}
    <table class="row actions">
      <tr>
        <td class="actions__cell">
          <table class="button main-action-cell">
            <tr>
              <td class="button__cell"><a href="{{ shop.url }}" class="button__text">ショップにアクセスする</a></td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
{% endif %}

            {% endif %}

            </td>
          </tr>
        </table>
      </center>
    </td>
  </tr>
</table>

          <table class="row section">
  <tr>
    <td class="section__cell">
      <center>
        <table class="container">
          <tr>
            <td>
              <h3>注文サマリー</h3>
            </td>
          </tr>
        </table>
        <table class="container">
          <tr>
            <td>
              
            {% if has_split_cart %}
              
<table class="row">
  {% for line in subtotal_line_items %}
    {% unless line.delivery_agreement %}
        {% if line.groups.size == 0 %}
          {% assign legacy_separator = true %}
          
<tr class="order-list__item">
  <td class="order-list__item__cell">
    <table>
        {% assign expand_bundles = false %}

      {% if expand_bundles and line.bundle_parent? %}
        <td class="order-list__parent-image-cell">
          {% if line.image %}
            <img src="{{ line | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
        </td>
      {% else %}
        <td class="order-list__image-cell">
          {% if line.image %}
            <img src="{{ line | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
        </td>
      {% endif %}
      <td class="order-list__product-description-cell">
        {% if line.presentment_title %}
          {% assign line_title = line.presentment_title %}
        {% elsif line.title %}
          {% assign line_title = line.title %}
        {% else %}
          {% assign line_title = line.product.title %}
        {% endif %}
        {% if line.quantity < line.quantity %}
          {% capture line_display %}
            {{ line.quantity }}/{{ line.quantity }}個
          {% endcapture %}
        {% else %}
          {% assign line_display = line.quantity %}
        {% endif %}

        <span class="order-list__item-title">{{ line_title }}&nbsp;&times;&nbsp;{{ line_display }}</span><br/>

        {% if line.variant.title != 'Default Title' and line.bundle_parent? == false %}
          <span class="order-list__item-variant">{{ line.variant.title }}</span><br/>
        {% elsif line.variant.title != 'Default Title' and line.bundle_parent? and expand_bundles == false %}
          <span class="order-list__item-variant">{{ line.variant.title }}</span><br/>
        {% endif %}

        {% if expand_bundles %}
          {% for component in line.bundle_components %}
            <table>
              <tr class="order-list__item">
                <td class="order-list__bundle-item">
                  <table>
                    <td class="order-list__image-cell">
                      {% if component.image %}
                        <img src="{{ component | img_url: 'compact_cropped' }}" align="left" width="40" height="40" class="order-list__product-image small"/>
                      {% else %}
                        <div class="order-list__no-image-cell small">
                          <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="40" height="40" class="order-list__no-product-image small"/>
                        </div>
                      {% endif %}
                    </td>

                    <td class="order-list__product-description-cell">
                      {% if component.product.title %}
                        {% assign component_title = component.product.title %}
                      {% else %}
                        {% assign component_title = component.title %}
                      {% endif %}

                      {% assign component_display = component.quantity %}

                      <span class="order-list__item-title">{{ component_display }}&nbsp;&times;&nbsp;{{ component_title }}</span><br>

                      {% if component.variant.title != 'Default Title'%}
                        <span class="order-list__item-variant">{{ component.variant.title }}</span>
                      {% endif %}
                    </td>
                  </table>
                </td>
              </tr>
            </table>
          {% endfor %}
        {% else %}
          {% for group in line.groups %}
            <span class="order-list__item-variant">{{ group.display_title }}の一部</span><br/>
          {% endfor %}
        {% endif %}

          {% if line.gift_card and line.properties["__shopify_send_gift_card_to_recipient"] %}
            {% for property in line.properties %}
  {% assign property_first_char = property.first | slice: 0 %}
  {% if property.last != blank and property_first_char != '_' %}
    <div class="order-list__item-property">
      <dt>{{ property.first }}:</dt>
      <dd>
      {% if property.last contains '/uploads/' %}
        <a href="{{ property.last }}" class="link" target="_blank">
        {{ property.last | split: '/' | last }}
        </a>
      {% else %}
        {{ property.last }}
      {% endif %}
      </dd>
    </div>
  {% endif %}
{% endfor %}

          {% endif %}

        {% if line.selling_plan_allocation %}
          <span class="order-list__item-variant">{{ line.selling_plan_allocation.selling_plan.name }}</span><br/>
        {% endif %}

        {% if line.refunded_quantity > 0 %}
          <span class="order-list__item-refunded">返金済み</span>
        {% endif %}

        {% if line.discount_allocations %}
          {% for discount_allocation in line.discount_allocations %}
            {% if discount_allocation.discount_application.target_selection != 'all' %}
            <p>
              <span class="order-list__item-discount-allocation">
                <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
                <span>
                  {{ discount_allocation.discount_application.title | upcase }}
                  (-{{ discount_allocation.amount | money }})
                </span>
              </span>
            </p>
            {% endif %}
          {% endfor %}
        {% endif %}
      </td>
        {% if expand_bundles and line.bundle_parent? %}
          <td class="order-list__parent-price-cell">
        {% else %}
          <td class="order-list__price-cell">
        {% endif %}
        {% if line.original_line_price != line.final_line_price %}
          <del class="order-list__item-original-price">{{ line.original_line_price | money }}</del>
        {% endif %}
          <p class="order-list__item-price">
            {% if line.final_line_price > 0 %}
              {{ line.final_line_price | money }}
              {% if line.unit_price_measurement %}
  <div class="order-list__unit-price">
    {{- line.unit_price | unit_price_with_measurement: line.unit_price_measurement -}}
  </div>
{% endif %}
            {% else %}
              無料
            {% endif %}
          </p>
        </td>
    </table>
  </td>
</tr>

        {% endif %}
    {% endunless %}
  {% endfor %}

    {% for line_item_group in line_item_groups %}
      {% unless line_item_group.components.first.delivery_agreement %}
        {% assign legacy_separator = true %}
        
{% assign final_line_price = 0 %}
{% assign original_line_price = 0 %}
{% assign discount_keys_str = "" %}

{% for component in line_item_group.components %}
  {% assign final_line_price = final_line_price | plus: component.final_line_price %}
  {% assign original_line_price = original_line_price | plus: component.original_line_price %}

  {% for da in component.discount_allocations %}
    {% if da.discount_application.target_selection != 'all' %}
      {% assign discount_key = da.discount_application.title | append: da.discount_application.type %}
      {% assign discount_keys_str = discount_keys_str | append: discount_key | append: "," %}
    {% endif %}
  {% endfor %}
{% endfor %}

{% assign discount_keys = discount_keys_str | split: "," | uniq %}

<tr class="order-list__item">
  <td class="order-list__item__cell">
    <table>
        <td class="order-list__parent-image-cell">
          {% if line_item_group.image %}
            <img src="{{ line_item_group | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
      </td>
      <td class="order-list__product-description-cell">
        <span class="order-list__item-title">{{ line_item_group.title }}&nbsp;&times;&nbsp;{{ line_item_group.quantity }}</span><br/>

        {% for discount_key in discount_keys %}
          {% assign discount_amount = 0 %}

          {% for component in line_item_group.components %}
            {% for da in component.discount_allocations %}
              {% assign key = da.discount_application.title | append: da.discount_application.type %}
              {% if da.discount_application.target_selection != 'all' and key == discount_key %}
                {% assign discount_amount = discount_amount | plus: da.amount %}
                {% assign discount_title = da.discount_application.title %}
              {% endif %}
            {% endfor %}
          {% endfor %}

          <p>
            <span class="order-list__item-discount-allocation">
              <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
              <span>
                {{ discount_title | upcase }}
                (-{{ discount_amount | money }})
              </span>
            </span>
          </p>
        {% endfor %}

        {% for component in line_item_group.components %}
          <table>
            <tr class="order-list__item">
              <td class="order-list__bundle-item">
                <table>
                  <td class="order-list__image-cell">
                    {% if component.image %}
                      <img src="{{ component | img_url: 'compact_cropped' }}" align="left" width="40" height="40" class="order-list__product-image"/>
                    {% else %}
                      <div class="order-list__no-image-cell small">
                        <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="40" height="40" class="order-list__no-product-image small"/>
                      </div>
                    {% endif %}
                  </td>

                  <td class="order-list__product-description-cell">
                    {% if component.product.title %}
                      {% assign component_title = component.product.title %}
                    {% else %}
                      {% assign component_title = component.title %}
                    {% endif %}

                    <span class="order-list__item-title component">{{ component.quantity }}&nbsp;&times;&nbsp;{{ component_title }}</span><br>

                    {% if component.variant.title != 'Default Title'%}
                      <span class="order-list__item-variant">{{ component.variant.title }}</span>
                    {% endif %}
                  </td>
                </table>
              </td>
            </tr>
          </table>
        {% endfor %}
      </td>

          <td class="order-list__parent-price-cell">
        {% if original_line_price != final_line_price %}
          <del class="order-list__item-original-price">{{ original_line_price | money }}</del>
        {% endif %}
          <p class="order-list__item-price">
            {% if final_line_price > 0 %}
              {{ final_line_price | money }}
             
            {% else %}
              無料
            {% endif %}
          </p>
        </td>
    </table>
  </td>
</tr>
      {% endunless %}
    {% endfor %}
</table>

{% if legacy_separator %}
  <hr class="order-list__delivery-method-type-separator">
{% endif %}

{% for delivery_agreement in delivery_agreements %}
  {% if delivery_agreement.line_items != blank %}
    {% if delivery_agreements.size > 1 %}
      <h4 class="order-list__delivery-method-type">
        {{ delivery_agreement.delivery_method_name }} 個のアイテム
      </h4>
    {% endif %}

    <table class="row">
      {% for line in delivery_agreement.line_items %}
          {% if line.groups.size == 0 %}
            
<tr class="order-list__item">
  <td class="order-list__item__cell">
    <table>
        {% assign expand_bundles = false %}

      {% if expand_bundles and line.bundle_parent? %}
        <td class="order-list__parent-image-cell">
          {% if line.image %}
            <img src="{{ line | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
        </td>
      {% else %}
        <td class="order-list__image-cell">
          {% if line.image %}
            <img src="{{ line | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
        </td>
      {% endif %}
      <td class="order-list__product-description-cell">
        {% if line.presentment_title %}
          {% assign line_title = line.presentment_title %}
        {% elsif line.title %}
          {% assign line_title = line.title %}
        {% else %}
          {% assign line_title = line.product.title %}
        {% endif %}
        {% if line.quantity < line.quantity %}
          {% capture line_display %}
            {{ line.quantity }}/{{ line.quantity }}個
          {% endcapture %}
        {% else %}
          {% assign line_display = line.quantity %}
        {% endif %}

        <span class="order-list__item-title">{{ line_title }}&nbsp;&times;&nbsp;{{ line_display }}</span><br/>

        {% if line.variant.title != 'Default Title' and line.bundle_parent? == false %}
          <span class="order-list__item-variant">{{ line.variant.title }}</span><br/>
        {% elsif line.variant.title != 'Default Title' and line.bundle_parent? and expand_bundles == false %}
          <span class="order-list__item-variant">{{ line.variant.title }}</span><br/>
        {% endif %}

        {% if expand_bundles %}
          {% for component in line.bundle_components %}
            <table>
              <tr class="order-list__item">
                <td class="order-list__bundle-item">
                  <table>
                    <td class="order-list__image-cell">
                      {% if component.image %}
                        <img src="{{ component | img_url: 'compact_cropped' }}" align="left" width="40" height="40" class="order-list__product-image small"/>
                      {% else %}
                        <div class="order-list__no-image-cell small">
                          <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="40" height="40" class="order-list__no-product-image small"/>
                        </div>
                      {% endif %}
                    </td>

                    <td class="order-list__product-description-cell">
                      {% if component.product.title %}
                        {% assign component_title = component.product.title %}
                      {% else %}
                        {% assign component_title = component.title %}
                      {% endif %}

                      {% assign component_display = component.quantity %}

                      <span class="order-list__item-title">{{ component_display }}&nbsp;&times;&nbsp;{{ component_title }}</span><br>

                      {% if component.variant.title != 'Default Title'%}
                        <span class="order-list__item-variant">{{ component.variant.title }}</span>
                      {% endif %}
                    </td>
                  </table>
                </td>
              </tr>
            </table>
          {% endfor %}
        {% else %}
          {% for group in line.groups %}
            <span class="order-list__item-variant">{{ group.display_title }}の一部</span><br/>
          {% endfor %}
        {% endif %}

          {% if line.gift_card and line.properties["__shopify_send_gift_card_to_recipient"] %}
            {% for property in line.properties %}
  {% assign property_first_char = property.first | slice: 0 %}
  {% if property.last != blank and property_first_char != '_' %}
    <div class="order-list__item-property">
      <dt>{{ property.first }}:</dt>
      <dd>
      {% if property.last contains '/uploads/' %}
        <a href="{{ property.last }}" class="link" target="_blank">
        {{ property.last | split: '/' | last }}
        </a>
      {% else %}
        {{ property.last }}
      {% endif %}
      </dd>
    </div>
  {% endif %}
{% endfor %}

          {% endif %}

        {% if line.selling_plan_allocation %}
          <span class="order-list__item-variant">{{ line.selling_plan_allocation.selling_plan.name }}</span><br/>
        {% endif %}

        {% if line.refunded_quantity > 0 %}
          <span class="order-list__item-refunded">返金済み</span>
        {% endif %}

        {% if line.discount_allocations %}
          {% for discount_allocation in line.discount_allocations %}
            {% if discount_allocation.discount_application.target_selection != 'all' %}
            <p>
              <span class="order-list__item-discount-allocation">
                <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
                <span>
                  {{ discount_allocation.discount_application.title | upcase }}
                  (-{{ discount_allocation.amount | money }})
                </span>
              </span>
            </p>
            {% endif %}
          {% endfor %}
        {% endif %}
      </td>
        {% if expand_bundles and line.bundle_parent? %}
          <td class="order-list__parent-price-cell">
        {% else %}
          <td class="order-list__price-cell">
        {% endif %}
        {% if line.original_line_price != line.final_line_price %}
          <del class="order-list__item-original-price">{{ line.original_line_price | money }}</del>
        {% endif %}
          <p class="order-list__item-price">
            {% if line.final_line_price > 0 %}
              {{ line.final_line_price | money }}
              {% if line.unit_price_measurement %}
  <div class="order-list__unit-price">
    {{- line.unit_price | unit_price_with_measurement: line.unit_price_measurement -}}
  </div>
{% endif %}
            {% else %}
              無料
            {% endif %}
          </p>
        </td>
    </table>
  </td>
</tr>

          {% endif %}
      {% endfor %}

        {% for line_item_group in delivery_agreement.line_item_groups %}
          
{% assign final_line_price = 0 %}
{% assign original_line_price = 0 %}
{% assign discount_keys_str = "" %}

{% for component in line_item_group.components %}
  {% assign final_line_price = final_line_price | plus: component.final_line_price %}
  {% assign original_line_price = original_line_price | plus: component.original_line_price %}

  {% for da in component.discount_allocations %}
    {% if da.discount_application.target_selection != 'all' %}
      {% assign discount_key = da.discount_application.title | append: da.discount_application.type %}
      {% assign discount_keys_str = discount_keys_str | append: discount_key | append: "," %}
    {% endif %}
  {% endfor %}
{% endfor %}

{% assign discount_keys = discount_keys_str | split: "," | uniq %}

<tr class="order-list__item">
  <td class="order-list__item__cell">
    <table>
        <td class="order-list__parent-image-cell">
          {% if line_item_group.image %}
            <img src="{{ line_item_group | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
      </td>
      <td class="order-list__product-description-cell">
        <span class="order-list__item-title">{{ line_item_group.title }}&nbsp;&times;&nbsp;{{ line_item_group.quantity }}</span><br/>

        {% for discount_key in discount_keys %}
          {% assign discount_amount = 0 %}

          {% for component in line_item_group.components %}
            {% for da in component.discount_allocations %}
              {% assign key = da.discount_application.title | append: da.discount_application.type %}
              {% if da.discount_application.target_selection != 'all' and key == discount_key %}
                {% assign discount_amount = discount_amount | plus: da.amount %}
                {% assign discount_title = da.discount_application.title %}
              {% endif %}
            {% endfor %}
          {% endfor %}

          <p>
            <span class="order-list__item-discount-allocation">
              <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
              <span>
                {{ discount_title | upcase }}
                (-{{ discount_amount | money }})
              </span>
            </span>
          </p>
        {% endfor %}

        {% for component in line_item_group.components %}
          <table>
            <tr class="order-list__item">
              <td class="order-list__bundle-item">
                <table>
                  <td class="order-list__image-cell">
                    {% if component.image %}
                      <img src="{{ component | img_url: 'compact_cropped' }}" align="left" width="40" height="40" class="order-list__product-image"/>
                    {% else %}
                      <div class="order-list__no-image-cell small">
                        <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="40" height="40" class="order-list__no-product-image small"/>
                      </div>
                    {% endif %}
                  </td>

                  <td class="order-list__product-description-cell">
                    {% if component.product.title %}
                      {% assign component_title = component.product.title %}
                    {% else %}
                      {% assign component_title = component.title %}
                    {% endif %}

                    <span class="order-list__item-title component">{{ component.quantity }}&nbsp;&times;&nbsp;{{ component_title }}</span><br>

                    {% if component.variant.title != 'Default Title'%}
                      <span class="order-list__item-variant">{{ component.variant.title }}</span>
                    {% endif %}
                  </td>
                </table>
              </td>
            </tr>
          </table>
        {% endfor %}
      </td>

          <td class="order-list__parent-price-cell">
        {% if original_line_price != final_line_price %}
          <del class="order-list__item-original-price">{{ original_line_price | money }}</del>
        {% endif %}
          <p class="order-list__item-price">
            {% if final_line_price > 0 %}
              {{ final_line_price | money }}
             
            {% else %}
              無料
            {% endif %}
          </p>
        </td>
    </table>
  </td>
</tr>
        {% endfor %}
    </table>

    {% unless forloop.last %}
      <hr class="order-list__delivery-method-type-separator">
    {% endunless %}
  {% endif %}
{% endfor %}

            {% else %}
              
  
<table class="row">
  {% for line in subtotal_line_items %}
    {% if line.groups.size == 0 %}
      
<tr class="order-list__item">
  <td class="order-list__item__cell">
    <table>
        {% assign expand_bundles = false %}

      {% if expand_bundles and line.bundle_parent? %}
        <td class="order-list__parent-image-cell">
          {% if line.image %}
            <img src="{{ line | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
        </td>
      {% else %}
        <td class="order-list__image-cell">
          {% if line.image %}
            <img src="{{ line | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
        </td>
      {% endif %}
      <td class="order-list__product-description-cell">
        {% if line.presentment_title %}
          {% assign line_title = line.presentment_title %}
        {% elsif line.title %}
          {% assign line_title = line.title %}
        {% else %}
          {% assign line_title = line.product.title %}
        {% endif %}
        {% if line.quantity < line.quantity %}
          {% capture line_display %}
            {{ line.quantity }}/{{ line.quantity }}個
          {% endcapture %}
        {% else %}
          {% assign line_display = line.quantity %}
        {% endif %}

        <span class="order-list__item-title">{{ line_title }}&nbsp;&times;&nbsp;{{ line_display }}</span><br/>

        {% if line.variant.title != 'Default Title' and line.bundle_parent? == false %}
          <span class="order-list__item-variant">{{ line.variant.title }}</span><br/>
        {% elsif line.variant.title != 'Default Title' and line.bundle_parent? and expand_bundles == false %}
          <span class="order-list__item-variant">{{ line.variant.title }}</span><br/>
        {% endif %}

        {% if expand_bundles %}
          {% for component in line.bundle_components %}
            <table>
              <tr class="order-list__item">
                <td class="order-list__bundle-item">
                  <table>
                    <td class="order-list__image-cell">
                      {% if component.image %}
                        <img src="{{ component | img_url: 'compact_cropped' }}" align="left" width="40" height="40" class="order-list__product-image small"/>
                      {% else %}
                        <div class="order-list__no-image-cell small">
                          <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="40" height="40" class="order-list__no-product-image small"/>
                        </div>
                      {% endif %}
                    </td>

                    <td class="order-list__product-description-cell">
                      {% if component.product.title %}
                        {% assign component_title = component.product.title %}
                      {% else %}
                        {% assign component_title = component.title %}
                      {% endif %}

                      {% assign component_display = component.quantity %}

                      <span class="order-list__item-title">{{ component_display }}&nbsp;&times;&nbsp;{{ component_title }}</span><br>

                      {% if component.variant.title != 'Default Title'%}
                        <span class="order-list__item-variant">{{ component.variant.title }}</span>
                      {% endif %}
                    </td>
                  </table>
                </td>
              </tr>
            </table>
          {% endfor %}
        {% else %}
          {% for group in line.groups %}
            <span class="order-list__item-variant">{{ group.display_title }}の一部</span><br/>
          {% endfor %}
        {% endif %}

          {% if line.gift_card and line.properties["__shopify_send_gift_card_to_recipient"] %}
            {% for property in line.properties %}
  {% assign property_first_char = property.first | slice: 0 %}
  {% if property.last != blank and property_first_char != '_' %}
    <div class="order-list__item-property">
      <dt>{{ property.first }}:</dt>
      <dd>
      {% if property.last contains '/uploads/' %}
        <a href="{{ property.last }}" class="link" target="_blank">
        {{ property.last | split: '/' | last }}
        </a>
      {% else %}
        {{ property.last }}
      {% endif %}
      </dd>
    </div>
  {% endif %}
{% endfor %}

          {% endif %}

        {% if line.selling_plan_allocation %}
          <span class="order-list__item-variant">{{ line.selling_plan_allocation.selling_plan.name }}</span><br/>
        {% endif %}

        {% if line.refunded_quantity > 0 %}
          <span class="order-list__item-refunded">返金済み</span>
        {% endif %}

        {% if line.discount_allocations %}
          {% for discount_allocation in line.discount_allocations %}
            {% if discount_allocation.discount_application.target_selection != 'all' %}
            <p>
              <span class="order-list__item-discount-allocation">
                <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
                <span>
                  {{ discount_allocation.discount_application.title | upcase }}
                  (-{{ discount_allocation.amount | money }})
                </span>
              </span>
            </p>
            {% endif %}
          {% endfor %}
        {% endif %}
      </td>
        {% if expand_bundles and line.bundle_parent? %}
          <td class="order-list__parent-price-cell">
        {% else %}
          <td class="order-list__price-cell">
        {% endif %}
        {% if line.original_line_price != line.final_line_price %}
          <del class="order-list__item-original-price">{{ line.original_line_price | money }}</del>
        {% endif %}
          <p class="order-list__item-price">
            {% if line.final_line_price > 0 %}
              {{ line.final_line_price | money }}
              {% if line.unit_price_measurement %}
  <div class="order-list__unit-price">
    {{- line.unit_price | unit_price_with_measurement: line.unit_price_measurement -}}
  </div>
{% endif %}
            {% else %}
              無料
            {% endif %}
          </p>
        </td>
    </table>
  </td>
</tr>

    {% endif %}
  {% endfor %}
  {% for line_item_group in line_item_groups %}
    
{% assign final_line_price = 0 %}
{% assign original_line_price = 0 %}
{% assign discount_keys_str = "" %}

{% for component in line_item_group.components %}
  {% assign final_line_price = final_line_price | plus: component.final_line_price %}
  {% assign original_line_price = original_line_price | plus: component.original_line_price %}

  {% for da in component.discount_allocations %}
    {% if da.discount_application.target_selection != 'all' %}
      {% assign discount_key = da.discount_application.title | append: da.discount_application.type %}
      {% assign discount_keys_str = discount_keys_str | append: discount_key | append: "," %}
    {% endif %}
  {% endfor %}
{% endfor %}

{% assign discount_keys = discount_keys_str | split: "," | uniq %}

<tr class="order-list__item">
  <td class="order-list__item__cell">
    <table>
        <td class="order-list__parent-image-cell">
          {% if line_item_group.image %}
            <img src="{{ line_item_group | img_url: 'compact_cropped' }}" align="left" width="60" height="60" class="order-list__product-image"/>
          {% else %}
            <div class="order-list__no-image-cell">
              <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="60" height="60" class="order-list__no-product-image"/>
            </div>
          {% endif %}
      </td>
      <td class="order-list__product-description-cell">
        <span class="order-list__item-title">{{ line_item_group.title }}&nbsp;&times;&nbsp;{{ line_item_group.quantity }}</span><br/>

        {% for discount_key in discount_keys %}
          {% assign discount_amount = 0 %}

          {% for component in line_item_group.components %}
            {% for da in component.discount_allocations %}
              {% assign key = da.discount_application.title | append: da.discount_application.type %}
              {% if da.discount_application.target_selection != 'all' and key == discount_key %}
                {% assign discount_amount = discount_amount | plus: da.amount %}
                {% assign discount_title = da.discount_application.title %}
              {% endif %}
            {% endfor %}
          {% endfor %}

          <p>
            <span class="order-list__item-discount-allocation">
              <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
              <span>
                {{ discount_title | upcase }}
                (-{{ discount_amount | money }})
              </span>
            </span>
          </p>
        {% endfor %}

        {% for component in line_item_group.components %}
          <table>
            <tr class="order-list__item">
              <td class="order-list__bundle-item">
                <table>
                  <td class="order-list__image-cell">
                    {% if component.image %}
                      <img src="{{ component | img_url: 'compact_cropped' }}" align="left" width="40" height="40" class="order-list__product-image"/>
                    {% else %}
                      <div class="order-list__no-image-cell small">
                        <img src="{{ 'notifications/no-image.png' | shopify_asset_url }}" align="left" width="40" height="40" class="order-list__no-product-image small"/>
                      </div>
                    {% endif %}
                  </td>

                  <td class="order-list__product-description-cell">
                    {% if component.product.title %}
                      {% assign component_title = component.product.title %}
                    {% else %}
                      {% assign component_title = component.title %}
                    {% endif %}

                    <span class="order-list__item-title component">{{ component.quantity }}&nbsp;&times;&nbsp;{{ component_title }}</span><br>

                    {% if component.variant.title != 'Default Title'%}
                      <span class="order-list__item-variant">{{ component.variant.title }}</span>
                    {% endif %}
                  </td>
                </table>
              </td>
            </tr>
          </table>
        {% endfor %}
      </td>

          <td class="order-list__parent-price-cell">
        {% if original_line_price != final_line_price %}
          <del class="order-list__item-original-price">{{ original_line_price | money }}</del>
        {% endif %}
          <p class="order-list__item-price">
            {% if final_line_price > 0 %}
              {{ final_line_price | money }}
             
            {% else %}
              無料
            {% endif %}
          </p>
        </td>
    </table>
  </td>
</tr>
  {% endfor %}
</table>

            {% endif %}
            <table class="row subtotal-lines">
  <tr>
    <td class="subtotal-spacer"></td>
    <td>
      <table class="row subtotal-table">

        
{% assign total_order_discount_amount = 0 %}
{% assign has_shipping_discount = false %}
{% assign epsilon = 0.00001 %}

{% for discount_application in discount_applications %}
  {% if discount_application.target_selection == 'all' and discount_application.target_type == 'line_item' %}
    {% assign order_discount_count = order_discount_count | plus: 1 %}
    {% assign total_order_discount_amount = total_order_discount_amount | plus: discount_application.total_allocated_amount %}
  {% endif %}
  {% if discount_application.target_type == 'shipping_line' %}
    {% assign has_shipping_discount = true %}
    {% assign shipping_discount_title = discount_application.title %}
    {% assign discount_value_price = discount_application.total_allocated_amount %}
    {% assign shipping_amount_minus_discount_value_price = shipping_price | minus: discount_value_price %}
    {% assign shipping_amount_minus_discount_value_price_abs = shipping_amount_minus_discount_value_price | abs %}
    {% assign discount_application_value_type = discount_application.value_type | strip %}
    {% if shipping_amount_minus_discount_value_price_abs < epsilon or discount_application_value_type == 'percentage' and discount_application.value == 100 %}
      {% assign free_shipping = true %}
    {% else %}
      {% assign discounted_shipping_price = shipping_amount_minus_discount_value_price %}
    {% endif %}
  {% endif %}
{% endfor %}



<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>小計</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ subtotal_price | plus: total_order_discount_amount | money }}</strong>
  </td>
</tr>



{% if order_discount_count > 0 %}
  {% if order_discount_count == 1 %}
    
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>注文のディスカウント</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>-{{ total_order_discount_amount | money }}</strong>
  </td>
</tr>

  {% endif %}
  {% if order_discount_count > 1 %}
    
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>注文のディスカウント</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>-{{ total_order_discount_amount | money }}</strong>
  </td>
</tr>

  {% endif %}
  {% for discount_application in discount_applications %}
    {% if discount_application.target_selection == 'all' and discount_application.target_type != 'shipping_line' %}
      <tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span class="subtotal-line__discount">
        <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
        <span class="subtotal-line__discount-title">
            {{ discount_application.title }} (-{{ discount_application.total_allocated_amount | money }})
        </span>
      </span>
    </p>
  </td>
</tr>

    {% endif %}
  {% endfor %}
{% endif %}


        {% unless retail_delivery_only %}
          {% if delivery_method == 'pick-up' %}
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>受取</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ shipping_price | money }}</strong>
  </td>
</tr>

          {% else %}
            {% if has_shipping_discount %}
  {% if free_shipping == true %}
    
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>配送</span>
    </p>
  </td>
  <td class="subtotal-line__value">
    <del>{% if shipping_price != 0 %}{{ shipping_price | money}}{% endif %} </del>
      <strong>無料</strong>
  </td>
</tr>

  {% else %}
    
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>配送</span>
    </p>
  </td>
  <td class="subtotal-line__value">
    <del>{{ shipping_price | money }} </del>
      <strong>{{ discounted_shipping_price | money }}</strong>
  </td>
</tr>

  {% endif %}
  <tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span class="subtotal-line__discount">
        <img src="{{ 'notifications/discounttag.png' | shopify_asset_url }}" width="18" height="18" class="discount-tag-icon" />
        <span class="subtotal-line__discount-title">
            {{ shipping_discount_title }} 
            {% if discount_value_price != 0 %}
              (-{{ discount_value_price | money }})
            {% endif %}
        </span>
      </span>
    </p>
  </td>
</tr>

{% else %}
  
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>配送</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ shipping_price | money }}</strong>
  </td>
</tr>

{% endif %}

          {% endif %}
        {% endunless %}

        {% if total_duties %}
          
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>関税</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ total_duties | money }}</strong>
  </td>
</tr>

        {% endif %}

        
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>税金合計</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ tax_price | money }}</strong>
  </td>
</tr>


        {% if total_tip and total_tip > 0 %}
          
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>Tip</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ total_tip | money }}</strong>
  </td>
</tr>

        {% endif %}
      </table>

      {% assign transaction_size = 0 %}
      {% assign transaction_amount = 0 %}
      {% assign net_transaction_amount_rounding = 0 %}
      {% assign authorized_amount = 0 %}
      {% assign has_refunds = false %}
      {% assign shopify_pay_captured = false %}
      {% assign shop_cash_offers_captured = false %}
      {% for transaction in transactions %}
        {% if transaction.status == "success" %}
          {% if transaction.kind == "sale" or transaction.kind == "capture"  %}
              {% if transaction.payment_details.credit_card_company %}
                {% assign shopify_pay_captured = true %}
              {% endif %}
              {% if transaction.gateway == "shop_cash" or transaction.gateway == "shop_offer" %}
                {% assign shop_cash_offers_captured = true %}
              {% endif %}
              {% assign transaction_size = transaction_size | plus: 1 %}
              {% assign transaction_amount = transaction_amount | plus: transaction.amount %}
              {% if transaction.amount_rounding != nil %}
                {% assign net_transaction_amount_rounding = net_transaction_amount_rounding | plus: transaction.amount_rounding %}
              {% endif %}
          {% elsif transaction.kind == "refund" or transaction.kind == "change" %}
            {% assign transaction_size = transaction_size | plus: 1 %}
            {% assign transaction_amount = transaction_amount | minus: transaction.amount %}
            {% assign has_refunds = true %}
            {% if transaction.amount_rounding != nil %}
              {% assign net_transaction_amount_rounding = net_transaction_amount_rounding | minus: transaction.amount_rounding %}
            {% endif %}
          {% elsif transaction.kind == "authorization" %}
            {% assign authorized_amount = authorized_amount | plus: transaction.amount %}
          {% endif %}
        {% endif %}
      {% endfor %}

      {% # Add shop cash/offer transactions to totals if shopify pay is captured and shop cash/offer is not captured yet %}
      {% if shopify_pay_captured == true and shop_cash_offers_captured == false %}
        {% for transaction in transactions %}
        {% if transaction.status == "success" %}
          {% if transaction.kind == "authorization" and transaction.gateway == "shop_cash" or transaction.gateway == "shop_offer" %}
              {% assign transaction_size = transaction_size | plus: 1 %}
              {% assign transaction_amount = transaction_amount | plus: transaction.amount %}
              {% if transaction.amount_rounding != nil %}
                {% assign net_transaction_amount_rounding = net_transaction_amount_rounding | plus: transaction.amount_rounding %}
              {% endif %}
          {% endif %}
        {% endif %}
      {% endfor %}
      {% endif %}
      <table class="row subtotal-table subtotal-table--total">
      {% if payment_terms and payment_terms.automatic_capture_at_fulfillment == false or b2b? %}
        {% assign next_payment = payment_terms.next_payment %}
        {% assign due_at_date = next_payment.due_at | date: "%b %d, %Y" %}
        {% if net_transaction_amount_rounding != 0 %}
          
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>合計</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ total_price | money_with_currency }}</strong>
  </td>
</tr>

          {% if total_discounts > 0 %}
            <tr class="subtotal-line">
              <td></td>
              <td class="subtotal-line__value total-discount">
                  割引 <span class="total-discount--amount">{{ total_discounts | money }}</span>
              </td>
            </tr>
          {% endif %}
          <tr><td colspan="2" class="subtotal-table__line"></td></tr>
          <div class="subtotal-line__value-small">
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>キャッシュの端数処理</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{% if net_transaction_amount_rounding < 0 %}-{% endif %} {{ net_transaction_amount_rounding | abs | money }}</strong>
  </td>
</tr>

          </div>
          <tr><td colspan="2" class="subtotal-table__line"></td></tr>
        {% endif %}
        
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>支払合計 (本日)</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ transaction_amount | plus: net_transaction_amount_rounding | money_with_currency }}</strong>
  </td>
</tr>

        <div class="payment-terms">
          {% assign next_amount_due = total_price %}
          {% if next_payment %}
            {% assign next_amount_due = next_payment.amount_due %}
          {% elsif total_outstanding > 0 %}
            {% assign next_amount_due = total_outstanding %}
          {% endif %}

          {% if payment_terms.type == 'receipt' %}
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>受領時の支払合計</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ next_amount_due | money_with_currency }}</strong>
  </td>
</tr>

          {% elsif payment_terms.type == 'fulfillment' %}
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>フルフィルメント時の支払合計</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ next_amount_due | money_with_currency }}</strong>
  </td>
</tr>

          {% else %}
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>支払合計期限: {{ due_at_date }}</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ next_amount_due | money_with_currency }}</strong>
  </td>
</tr>

          {% endif %}
        </div>
        {% if total_discounts > 0 and net_transaction_amount_rounding == 0 %}
          <tr class="subtotal-line">
            <td></td>
            <td class="subtotal-line__value total-discount">
                割引 <span class="total-discount--amount">{{ total_discounts | money }}</span>
            </td>
          </tr>
        {% endif %}
      {% else %}
        
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>合計</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ total_price | money_with_currency }}</strong>
  </td>
</tr>

        {% if total_discounts > 0 %}
          <tr class="subtotal-line">
            <td></td>
            <td class="subtotal-line__value total-discount">
                割引 <span class="total-discount--amount">{{ total_discounts | money }}</span>
            </td>
          </tr>
        {% endif %}
        {% if net_transaction_amount_rounding != 0 %}
          <tr><td colspan="2" class="subtotal-table__line"></td></tr>
          <div class="subtotal-line__value-small">
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>キャッシュの端数処理</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{% if net_transaction_amount_rounding < 0 %}-{% endif %} {{ net_transaction_amount_rounding | abs | money }}</strong>
  </td>
</tr>

          </div>
          {% if financial_status == 'paid' %}
            
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>支払い済み</span>
        <br>
        <small>{{ order.transactions | map: 'gateway_display_name' | uniq | join: ', ' }}</small>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ transaction_amount | plus: net_transaction_amount_rounding | money_with_currency }}</strong>
  </td>
</tr>

          {% endif %}
        {% endif %}
        {% if transaction_amount != total_price and payment_terms == nil%}
          {% if transaction_amount == 0 and authorized_amount > 0 and has_refunds == false %}
          {% else %}
            <div class="payment-terms">
              
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>支払合計 (本日)</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ transaction_amount | plus: net_transaction_amount_rounding | money_with_currency }}</strong>
  </td>
</tr>

            </div>
          {% endif %}
        {% endif %}
      {% endif %}
      </table>

      {% unless payment_terms %}
      {% if transaction_size > 1 or transaction_amount < total_price %}
        <table class="row subtotal-table">
          <tr><td colspan="2" class="subtotal-table__line"></td></tr>
          <tr><td colspan="2" class="subtotal-table__small-space"></td></tr>

          {% for transaction in transactions %}
            {% assign amount_rounding = 0 %}
            {% if transaction.amount_rounding != 0 %}
              {% assign amount_rounding =  transaction.amount_rounding %}
            {% endif %}
            {% if transaction.status == "success" and transaction.kind == "capture" or transaction.kind == "sale" %}
              {% if transaction.payment_details.gift_card_last_four_digits %}
                {% capture transaction_name %}ギフトカード (下4桁が{{ transaction.payment_details.gift_card_last_four_digits }}){% endcapture %}
              {% elsif transaction.payment_details.credit_card_company %}
                {% capture transaction_name %}{{ transaction.payment_details.credit_card_company }} (カード番号下4桁: {{ transaction.payment_details.credit_card_last_four_digits }}){% endcapture %}
              {% else %}
                {% capture transaction_name %}{{ transaction.gateway_display_name }}{% endcapture %}
              {% endif %}

              
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>{{transaction_name}}</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ transaction.amount | plus: amount_rounding | money }}</strong>
  </td>
</tr>

            {% elsif shopify_pay_captured and shop_cash_offers_captured == false and transaction.kind == "authorization" and transaction.gateway == "shop_cash" or transaction.gateway == "shop_offer" %}
              {% capture transaction_name %}{{ transaction.gateway_display_name }}{% endcapture %}

              
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>{{transaction_name}}</span>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>{{ transaction.amount | plus: amount_rounding | money }}</strong>
  </td>
</tr>

            {% endif %}
            {% if transaction.kind == 'refund' and transaction.gateway != "shop_offer" %}
              {% if transaction.payment_details.gift_card_last_four_digits %}
                {% assign refund_method_title = transaction.payment_details.type %}
              {% elsif transaction.payment_details.credit_card_company %}
                {% assign refund_method_title = transaction.payment_details.credit_card_company %}
              {% else %}
                {% assign refund_method_title = transaction.gateway_display_name %}
              {% endif %}

              
<tr class="subtotal-line">
  <td class="subtotal-line__title">
    <p>
      <span>返金</span>
        <br>
        <small>{{ refund_method_title | replace: '_', ' ' | capitalize }}</small>
    </p>
  </td>
  <td class="subtotal-line__value">
      <strong>- {{ transaction.amount | plus: amount_rounding | money }}</strong>
  </td>
</tr>

            {% endif %}
          {% endfor %}
        </table>
      {% endif %}


      {% endunless %}
    </td>
  </tr>
</table>


            </td>
          </tr>
        </table>
      </center>
    </td>
  </tr>
</table>

          <table class="row section">
  <tr>
    <td class="section__cell">
      <center>
        <table class="container">
          <tr>
            <td>
              <h3>お客様情報</h3>
            </td>
          </tr>
        </table>
        <table class="container">
          <tr>
            <td>
              
            <table class="row">
              <tr>
                {% if requires_shipping and shipping_address %}
                  <td class="customer-info__item">
                    <h4>配送先住所</h4>
                    {{ shipping_address | format_address }}
                  </td>
                {% endif %}
                {% if billing_address %}
                  <td class="customer-info__item">
                    <h4>請求先住所</h4>
                    {{ billing_address | format_address }}
                  </td>
                {% endif %}
              </tr>
            </table>
            <table class="row">
              <tr>
                {% if company_location %}
                  <td class="customer-info__item">
                    <h4>ロケーション</h4>
                    <p>
                      {{ company_location.name }}
                    </p>
                  </td>
                {% endif %}
                {% if transaction_size > 0 or payment_terms and payment_terms.automatic_capture_at_fulfillment == false or b2b? %}
                  <td class="customer-info__item">
                    <h4>決済</h4>
                    <p class="customer-info__item-content">
                      {% if payment_terms %}
                        {% assign due_date = payment_terms.next_payment.due_at | default: nil %}
                        {% if payment_terms.type == 'receipt' or payment_terms.type == 'fulfillment' and payment_terms.next_payment.due_at == nil %}
                          {{ payment_terms.translated_name }}<br>
                        {% else %}
                          {{ payment_terms.translated_name }}：{{ due_date | date: format: 'date' }}が期限<br>
                        {% endif %}
                      {% endif %}
                      {% if transaction_size > 0 %}
                        {% for transaction in transactions %}
                          {% if transaction.status == "success" or transaction.status == "pending" %}
                            {% if transaction.kind == "capture" or transaction.kind == "sale" %}
                              {% if transaction.payment_details.gift_card_last_four_digits %}
                                <img src="{{ transaction.payment_details.type | downcase | replace: '_', '-'  | payment_type_img_url }}" class="customer-info__item-credit" height="24">
                                下4桁{{ transaction.payment_details.gift_card_last_four_digits }}<br>
                              {% elsif transaction.payment_details.type == "shop_pay_installments" and transaction.payment_details.credit_card_company == "unknown" %}
                                <img src="{{ 'notifications/shop-pay.svg' | shopify_asset_url  }}" class="customer-info__item-shop-pay">
                                <br>
                                <span>Shop Payの分割払いで支払い済み</span><br>
                              {% elsif transaction.payment_details.credit_card_company %}
                                <img src="{{ transaction.payment_details.credit_card_company | payment_icon_png_url  }}" class="customer-info__item-credit" height="24" alt="{{ transaction.payment_details.credit_card_company }}">
                                <span>下4桁{{ transaction.payment_details.credit_card_last_four_digits }}</span><br>
                              {% elsif transaction.gateway_display_name == "Gift card" %}
                                <img src="{{ transaction.gateway_display_name | downcase | replace: ' ', '-'  | payment_type_img_url }}" class="customer-info__item-credit" height="24">
                                下4桁{{ transaction.payment_details.gift_card.last_four_characters | upcase }}<br>
                                  &emsp;&emsp;&emsp;&nbsp;ギフトカード残高 - <b>{{ transaction.payment_details.gift_card.balance |  money }}</b>
                              {% elsif transaction.gateway_display_name != "Shop Cash" and transaction.gateway != "shop_offer" %}
                                {{ transaction.gateway_display_name }}<br>
                              {% endif %}
                            {% elsif transaction.kind == "authorization" and transaction.gateway_display_name == "Shop Cash" %}
                              <span>Shop Cash</span><br>
                            {% endif %}
                          {% endif %}
                        {% endfor %}
                      {% endif %}
                    </p>
                  </td>
                {% endif %}
              </tr>
              <tr>
                {% if requires_shipping and shipping_address %}
                  {% if shipping_method %}
                    <td class="customer-info__item">
                      <h4>配送方法</h4>
                        <p>
                          {% if delivery_promise_branded_shipping_line %}
                            {{ delivery_promise_branded_shipping_line }}
                          {% else %}
                            {{ shipping_method.title }}
                          {% endif %}
                        </p>
                    </td>
                  {% endif %}
                {% endif %}
              </tr>
            </table>

            </td>
          </tr>
        </table>
      </center>
    </td>
  </tr>
</table>

          <table class="row footer">
  <tr>
    <td class="footer__cell">
      <center>
        <table class="container">
          <tr>
            <td>
              
              <p class="disclaimer__subtext">ご不明な点がございましたら、このメールにご返信いただくか、<a href="mailto:{{ shop.email }}">{{ shop.email }}</a>までご連絡ください。</p>
            </td>
          </tr>
        </table>
      </center>
    </td>
  </tr>
</table>

<img src="{{ 'notifications/spacer.png' | shopify_asset_url }}" class="spacer" height="1" />

        </td>
      </tr>
    </table>
  </body>
</html>

{%- if billing_address.country_code == 'DE' or billing_address.country_code == 'DK' -%}
  {%- if shop.terms_of_service.body != blank -%}
    {{ shop.terms_of_service | attach_as_pdf: "利用規約" }}
  {%- endif -%}

  {%- if shop.refund_policy.body != blank -%}
    {{ shop.refund_policy | attach_as_pdf: "返金ポリシー" }}
  {%- endif -%}
{%- endif -%}
